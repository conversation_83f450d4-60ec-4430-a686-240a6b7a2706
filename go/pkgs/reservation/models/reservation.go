package models

import (
	"contentmanager/pkgs/reservation/shared"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"time"
)

type Reservation struct {
	Key            string
	CurrentEditor  *uuid.UUID
	EditingSession *time.Time
	ExtendedLock   *time.Time
}

func (Reservation) TableName() string {
	return "reservations"
}

func (r Reservation) AvailableFor(userID uuid.UUID, editingSessionKey time.Time) error {
	if r.CurrentEditor == nil {
		return nil
	}

	if *r.CurrentEditor == userID {
		if !r.ValidEditingSession() {
			return nil
		}

		if *r.EditingSession == editingSessionKey {
			return nil
		}

		return shared.NewConflictError(fmt.Sprintf("Editing session conflict for session key %v",
			editingSessionKey), r)
	}

	if r.ValidExtendedLock() {
		return shared.NewConflictError(fmt.Sprintf("Extended lock conflict for user %v",
			userID), r)
	}

	if r.ValidEditingSession() {
		return shared.NewConflictError(fmt.Sprintf("Editing session conflict for user %v",
			userID), r)
	}

	return nil
}

func (r Reservation) ValidEditingSession() bool {
	return r.EditingSession != nil && r.EditingSession.Before(time.Now())
}

func (r Reservation) ValidExtendedLock() bool {
	return r.ExtendedLock != nil && r.ExtendedLock.Before(time.Now())
}
