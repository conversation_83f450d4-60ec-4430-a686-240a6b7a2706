package gormxx

import (
	"contentmanager/library/shared"
	"contentmanager/pkgs/auth/identity"
	uuid "github.com/satori/go.uuid"
	"testing"
	"time"
)

func Test_ReservationPlugin(t *testing.T) {
	appContext := shared.NewMockAppContextBasic(shared.MockAppContextBasicParams{
		TenantID:  uuid.UUID{},
		SiteID:    uuid.UUID{},
		TenantDB:  nil,
		TenancyDB: nil,
		Identity: &identity.Account{
			ID: uuid.NewV4(),
		},
		Request: nil,
	})

	_ = appContext

	t.Log(time.Now().Format(time.RFC3339))
}
